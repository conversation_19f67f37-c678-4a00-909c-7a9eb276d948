# 应用推荐网站 - ICBar AllInOne

基于"送码活动"的应用推荐平台，让用户发现优质应用并参与送码活动获取免费邀请码。

## 🌟 项目特色

- **独特排名机制**: 应用排名基于"送码活动"数量和热度，而非传统的下载量或评分
- **送码活动系统**: 核心功能，允许用户分享应用邀请码
- **VIP会员系统**: 提供高级功能和特权
- **实名认证**: 确保用户身份真实性
- **多语言支持**: 支持中文和英文
- **响应式设计**: 完美适配移动端和桌面端

## 🏗️ 项目架构

```
ICBar-AllInOne/
├── frontend/          # Next.js 前端应用
├── backend/           # Express.js 后端API
└── README.md         # 项目说明文档
```

### 前端技术栈
- **框架**: Next.js 15 + React 19
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **认证**: NextAuth.js + Google OAuth
- **状态管理**: React Hooks
- **国际化**: next-i18next

### 后端技术栈
- **框架**: Express.js + TypeScript
- **数据库**: Supabase (PostgreSQL)
- **认证**: JWT + bcryptjs
- **验证**: Zod
- **日志**: Winston
- **安全**: Helmet + CORS + Rate Limiting

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn
- Supabase 账户

### 1. 克隆项目
```bash
git clone https://github.com/leuoson-coding/ICBar-Web.git
cd ICBar-AllInOne
```

### 2. 数据库设置
1. 创建 Supabase 项目
2. 在 Supabase SQL Editor 中执行以下脚本：
   - `backend/database/schema.sql` - 创建表结构
   - `backend/database/rls-policies.sql` - 设置安全策略
   - `backend/database/sample-data.sql` - 插入示例数据（可选）

### 3. 后端设置
```bash
cd backend
npm install
cp .env.example .env
# 编辑 .env 文件，填入你的配置信息
npm run dev
```

### 4. 前端设置
```bash
cd frontend
npm install
cp .env.local.example .env.local
# 编辑 .env.local 文件，填入你的配置信息
npm run dev
```

### 5. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:3001

## 📋 核心功能

### 用户系统
- [x] 用户注册/登录（Google OAuth）
- [x] 实名认证功能
- [x] VIP会员系统
- [x] 用户订阅应用功能

### 应用管理
- [x] 用户可以发布/添加应用
- [x] 应用详情页面
- [x] 应用排名系统（基于送码活动和热度）
- [x] 应用分类和搜索

### 送码活动系统
- [x] 创建送码活动
- [x] 邀请码管理
- [x] VIP用户高级条件设置
- [x] 反馈问卷系统
- [x] 活动统计和管理

## 🔧 配置说明

### 环境变量配置

#### 后端 (.env)
```env
# 服务器配置
PORT=3001
NODE_ENV=development

# Supabase 配置
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# JWT 配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# CORS 配置
FRONTEND_URL=http://localhost:3000
```

#### 前端 (.env.local)
```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# NextAuth 配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# API 配置
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

## 📚 API 文档

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/refresh` - 刷新token
- `GET /api/auth/verify` - 验证token

### 用户相关
- `GET /api/users/me` - 获取当前用户信息
- `PATCH /api/users/me` - 更新用户信息
- `POST /api/users/verify` - 实名认证
- `POST /api/users/upgrade-vip` - 升级VIP

### 应用相关
- `GET /api/apps` - 获取应用列表
- `GET /api/apps/:id` - 获取应用详情
- `POST /api/apps` - 创建应用
- `PATCH /api/apps/:id` - 更新应用
- `DELETE /api/apps/:id` - 删除应用

### 活动相关
- `GET /api/activities` - 获取活动列表
- `GET /api/activities/:id` - 获取活动详情
- `POST /api/activities` - 创建活动
- `POST /api/activities/:id/use-code` - 使用邀请码
- `POST /api/activities/:id/feedback` - 提交反馈

## 🛠️ 开发指南

### 项目结构
```
frontend/
├── src/
│   ├── app/              # Next.js App Router
│   ├── components/       # React 组件
│   ├── hooks/           # 自定义 Hooks
│   ├── lib/             # 工具库
│   ├── types/           # TypeScript 类型
│   └── utils/           # 工具函数
├── public/              # 静态资源
└── package.json

backend/
├── src/
│   ├── controllers/     # 控制器
│   ├── middleware/      # 中间件
│   ├── routes/         # 路由
│   ├── services/       # 服务层
│   ├── types/          # TypeScript 类型
│   ├── utils/          # 工具函数
│   └── config/         # 配置文件
├── database/           # 数据库脚本
└── package.json
```

### 开发命令
```bash
# 前端
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查

# 后端
npm run dev          # 启动开发服务器
npm run build        # 编译 TypeScript
npm run start        # 启动生产服务器
npm run lint         # 代码检查
```

## 🔒 安全特性

- **Row Level Security (RLS)**: 数据库级别的安全策略
- **JWT认证**: 安全的用户认证机制
- **速率限制**: 防止API滥用
- **输入验证**: 使用Zod进行数据验证
- **CORS配置**: 跨域请求安全控制
- **Helmet**: HTTP安全头设置

## 📈 部署指南

### 前端部署 (Vercel 推荐)
1. 连接GitHub仓库到Vercel
2. 配置环境变量
3. 自动部署

### 后端部署
1. 选择云服务提供商（如Railway、Heroku、AWS等）
2. 配置环境变量
3. 部署应用

### 数据库
- 使用Supabase托管PostgreSQL数据库
- 配置备份和监控

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目链接: [https://github.com/leuoson-coding/ICBar-Web](https://github.com/leuoson-coding/ICBar-Web)
- 问题反馈: [Issues](https://github.com/leuoson-coding/ICBar-Web/issues)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！