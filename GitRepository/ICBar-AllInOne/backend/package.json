{"name": "app-recommendation-backend", "version": "1.0.0", "description": "Backend API for App Recommendation Site", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "test-server": "tsx watch src/test-server.ts", "simple-test": "tsx watch src/simple-test.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "zod": "^3.22.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "rate-limiter-flexible": "^3.0.8", "winston": "^3.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "typescript": "^5.3.0", "tsx": "^4.6.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["api", "backend", "app-recommendation", "supabase", "express"], "author": "Your Name", "license": "MIT"}