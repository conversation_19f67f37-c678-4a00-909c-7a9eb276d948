import { createClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// 创建一个检查函数
function checkSupabaseConfig() {
  if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
    logger.error('Missing Supabase configuration. Please check your environment variables.');
    throw new Error('Missing Supabase configuration');
  }
}

// 延迟初始化的客户端
let supabase: any = null;
let supabaseAdmin: any = null;

function getSupabase() {
  if (!supabase) {
    checkSupabaseConfig();
    supabase = createClient(supabaseUrl!, supabaseAnonKey!);
    logger.info('Supabase client initialized');
  }
  return supabase;
}

function getSupabaseAdmin() {
  if (!supabaseAdmin) {
    checkSupabaseConfig();
    supabaseAdmin = createClient(supabaseUrl!, supabaseServiceKey!, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    logger.info('Supabase admin client initialized');
  }
  return supabaseAdmin;
}

// 导出getter函数
export { getSupabase as supabase, getSupabaseAdmin as supabaseAdmin };
