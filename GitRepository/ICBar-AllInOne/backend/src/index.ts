import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { rateLimiter } from '@/middleware/rateLimiter';

// 动态导入路由（避免在环境变量加载前导入）
let authRoutes: any, userRoutes: any, appRoutes: any, activityRoutes: any;

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet());

// CORS配置
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// 请求解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 速率限制
app.use(rateLimiter);

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV 
  });
});

// 动态加载路由
async function loadRoutes() {
  try {
    const { authRoutes: auth } = await import('@/routes/auth');
    const { userRoutes: users } = await import('@/routes/users');
    const { appRoutes: apps } = await import('@/routes/apps');
    const { activityRoutes: activities } = await import('@/routes/activities');

    authRoutes = auth;
    userRoutes = users;
    appRoutes = apps;
    activityRoutes = activities;

    // API路由
    app.use('/api/auth', authRoutes);
    app.use('/api/users', userRoutes);
    app.use('/api/apps', appRoutes);
    app.use('/api/activities', activityRoutes);

    logger.info('Routes loaded successfully');
  } catch (error) {
    logger.error('Failed to load routes:', error);
    // 如果路由加载失败，提供基本的健康检查
  }
}

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'API endpoint not found',
    path: req.originalUrl 
  });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
async function startServer() {
  await loadRoutes();

  app.listen(PORT, () => {
    logger.info(`Server is running on port ${PORT}`);
    logger.info(`Environment: ${process.env.NODE_ENV}`);
    logger.info(`Frontend URL: ${process.env.FRONTEND_URL}`);
  });
}

startServer().catch(error => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
