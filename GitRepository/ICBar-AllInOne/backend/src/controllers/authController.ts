import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { supabaseAdmin } from '@/config/supabase';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

export const authController = {
  // 用户注册
  async register(req: Request, res: Response) {
    const { email, password, name } = req.body;

    if (!email || !password) {
      throw createError('Email and password are required', 400);
    }

    // 检查用户是否已存在
    const { data: existingUser } = await supabaseAdmin()
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      throw createError('User already exists', 409);
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建用户
    const { data: user, error } = await supabaseAdmin()
      .from('users')
      .insert({
        email,
        name,
        password_hash: hashedPassword,
      })
      .select('id, email, name, is_verified, is_vip, created_at')
      .single();

    if (error) {
      logger.error('User registration error:', error);
      throw createError('Failed to create user', 500);
    }

    // 生成JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.status(201).json({
      message: 'User registered successfully',
      data: {
        user,
        token
      }
    });
  },

  // 用户登录
  async login(req: Request, res: Response) {
    const { email, password } = req.body;

    if (!email || !password) {
      throw createError('Email and password are required', 400);
    }

    // 查找用户
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id, email, name, password_hash, is_verified, is_vip, created_at')
      .eq('email', email)
      .single();

    if (error || !user) {
      throw createError('Invalid credentials', 401);
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      throw createError('Invalid credentials', 401);
    }

    // 生成JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // 移除密码哈希
    const { password_hash, ...userWithoutPassword } = user;

    res.json({
      message: 'Login successful',
      data: {
        user: userWithoutPassword,
        token
      }
    });
  },

  // 刷新token
  async refresh(req: Request, res: Response) {
    const { token } = req.body;

    if (!token) {
      throw createError('Token is required', 400);
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      
      // 验证用户仍然存在
      const { data: user, error } = await supabaseAdmin
        .from('users')
        .select('id, email, name, is_verified, is_vip')
        .eq('id', decoded.userId)
        .single();

      if (error || !user) {
        throw createError('Invalid token', 401);
      }

      // 生成新token
      const newToken = jwt.sign(
        { userId: user.id, email: user.email },
        process.env.JWT_SECRET!,
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      res.json({
        message: 'Token refreshed successfully',
        data: {
          user,
          token: newToken
        }
      });
    } catch (error: any) {
      if (error.name === 'TokenExpiredError') {
        throw createError('Token expired', 401);
      }
      throw createError('Invalid token', 401);
    }
  },

  // 用户登出
  async logout(req: Request, res: Response) {
    // 在无状态JWT系统中，登出主要在客户端处理
    res.json({
      message: 'Logout successful'
    });
  },

  // 验证token
  async verify(req: Request, res: Response) {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError('No token provided', 401);
    }

    const token = authHeader.substring(7);

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      
      const { data: user, error } = await supabaseAdmin
        .from('users')
        .select('id, email, name, is_verified, is_vip, created_at')
        .eq('id', decoded.userId)
        .single();

      if (error || !user) {
        throw createError('Invalid token', 401);
      }

      res.json({
        message: 'Token is valid',
        data: { user }
      });
    } catch (error: any) {
      if (error.name === 'TokenExpiredError') {
        throw createError('Token expired', 401);
      }
      throw createError('Invalid token', 401);
    }
  }
};
