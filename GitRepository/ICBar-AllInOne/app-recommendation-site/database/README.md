# 数据库设计文档

## 概述

本项目使用 Supabase 作为后端数据库服务，采用 PostgreSQL 数据库。数据库设计支持应用推荐网站的核心功能，包括用户管理、应用管理、送码活动系统等。

## 数据库表结构

### 1. users (用户表)
存储用户基本信息和认证状态。

**字段说明：**
- `id`: 用户唯一标识符 (UUID)
- `email`: 用户邮箱地址
- `name`: 用户姓名
- `avatar_url`: 头像URL
- `is_verified`: 是否已实名认证
- `is_vip`: 是否为VIP用户
- `verification_data`: 实名认证相关数据 (JSONB)

### 2. apps (应用表)
存储应用信息和统计数据。

**字段说明：**
- `id`: 应用唯一标识符 (UUID)
- `name`: 应用名称
- `description`: 应用描述
- `category`: 应用分类 (枚举类型)
- `creator_id`: 创建者用户ID
- `activity_count`: 活动数量统计
- `total_codes_distributed`: 总分发邀请码数量

### 3. activities (送码活动表)
存储送码活动的详细信息。

**字段说明：**
- `id`: 活动唯一标识符 (UUID)
- `app_id`: 关联的应用ID
- `title`: 活动标题
- `total_codes`: 总邀请码数量
- `remaining_codes`: 剩余邀请码数量
- `requires_verification`: 是否需要实名认证
- `requires_subscription`: 是否需要订阅应用
- `feedback_required`: 是否需要反馈
- `feedback_questions`: 反馈问题列表 (JSONB)

### 4. invite_codes (邀请码表)
存储具体的邀请码信息。

**字段说明：**
- `id`: 邀请码唯一标识符 (UUID)
- `activity_id`: 关联的活动ID
- `code`: 邀请码内容
- `is_used`: 是否已使用
- `used_by`: 使用者用户ID
- `used_at`: 使用时间

### 5. user_subscriptions (用户订阅表)
存储用户对应用的订阅关系。

### 6. feedbacks (反馈表)
存储用户对活动的反馈信息。

## 安全策略 (RLS)

项目使用 Supabase 的 Row Level Security (RLS) 来确保数据安全：

1. **用户数据保护**: 用户只能访问和修改自己的数据
2. **应用权限控制**: 只有应用创建者可以管理自己的应用
3. **活动访问限制**: 确保用户只能参与符合条件的活动
4. **邀请码安全**: 防止邀请码被恶意获取或滥用

## 数据库设置步骤

### 1. 创建 Supabase 项目
1. 访问 [Supabase](https://supabase.com)
2. 创建新项目
3. 记录项目URL和API密钥

### 2. 执行数据库脚本
按以下顺序执行SQL脚本：

1. **创建表结构**:
   ```sql
   -- 在 Supabase SQL Editor 中执行
   -- 复制 schema.sql 的内容并执行
   ```

2. **设置安全策略**:
   ```sql
   -- 执行 rls-policies.sql 的内容
   ```

3. **插入示例数据** (可选):
   ```sql
   -- 执行 sample-data.sql 的内容
   ```

### 3. 配置环境变量
在项目根目录创建 `.env.local` 文件：

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 4. 启用认证服务
在 Supabase 控制台中：
1. 进入 Authentication 设置
2. 启用 Google OAuth 提供商
3. 配置 Google OAuth 客户端ID和密钥

## 性能优化

### 索引策略
数据库已创建以下索引来优化查询性能：
- 应用表：按创建者、分类、活动数量、创建时间索引
- 活动表：按应用、创建者、状态、开始时间索引
- 邀请码表：按活动、邀请码、使用状态索引

### 触发器
- **自动更新时间戳**: 自动更新 `updated_at` 字段
- **统计数据维护**: 自动更新应用的活动统计
- **剩余码数更新**: 自动更新活动的剩余邀请码数量

## 数据备份和恢复

Supabase 提供自动备份功能，建议：
1. 启用自动备份
2. 定期导出重要数据
3. 测试恢复流程

## 监控和维护

建议监控以下指标：
1. 数据库连接数
2. 查询性能
3. 存储使用量
4. API调用频率

## 扩展性考虑

当数据量增长时，可以考虑：
1. 分区大表（如邀请码表）
2. 使用读取副本
3. 实施缓存策略
4. 优化查询和索引
