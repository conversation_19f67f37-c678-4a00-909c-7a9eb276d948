-- Row Level Security (RLS) 策略
-- 这些策略确保用户只能访问他们有权限的数据

-- 用户表策略
-- 用户可以查看所有用户的基本信息（用于显示创建者等）
CREATE POLICY "Users can view all users basic info" ON users
    FOR SELECT USING (true);

-- 用户只能更新自己的信息
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- 用户可以插入自己的记录（注册时）
CREATE POLICY "Users can insert own record" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 应用表策略
-- 所有人都可以查看已激活的应用
CREATE POLICY "Anyone can view active apps" ON apps
    FOR SELECT USING (is_active = true);

-- 用户可以创建应用
CREATE POLICY "Authenticated users can create apps" ON apps
    FOR INSERT WITH CHECK (auth.uid() = creator_id);

-- 用户只能更新自己创建的应用
CREATE POLICY "Users can update own apps" ON apps
    FOR UPDATE USING (auth.uid() = creator_id);

-- 用户只能删除自己创建的应用
CREATE POLICY "Users can delete own apps" ON apps
    FOR DELETE USING (auth.uid() = creator_id);

-- 送码活动表策略
-- 所有人都可以查看激活的活动
CREATE POLICY "Anyone can view active activities" ON activities
    FOR SELECT USING (is_active = true);

-- 用户可以创建活动（需要是应用的创建者）
CREATE POLICY "App creators can create activities" ON activities
    FOR INSERT WITH CHECK (
        auth.uid() = creator_id AND 
        EXISTS (
            SELECT 1 FROM apps 
            WHERE apps.id = activities.app_id 
            AND apps.creator_id = auth.uid()
        )
    );

-- 用户只能更新自己创建的活动
CREATE POLICY "Users can update own activities" ON activities
    FOR UPDATE USING (auth.uid() = creator_id);

-- 用户只能删除自己创建的活动
CREATE POLICY "Users can delete own activities" ON activities
    FOR DELETE USING (auth.uid() = creator_id);

-- 邀请码表策略
-- 用户可以查看活动的邀请码（但不包括具体的code内容，除非是创建者）
CREATE POLICY "Users can view invite codes info" ON invite_codes
    FOR SELECT USING (
        -- 活动创建者可以看到所有信息
        EXISTS (
            SELECT 1 FROM activities 
            WHERE activities.id = invite_codes.activity_id 
            AND activities.creator_id = auth.uid()
        )
        OR
        -- 其他用户只能看到已使用的码的基本信息（不包括code本身）
        (is_used = true AND used_by = auth.uid())
    );

-- 只有活动创建者可以插入邀请码
CREATE POLICY "Activity creators can insert invite codes" ON invite_codes
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM activities 
            WHERE activities.id = invite_codes.activity_id 
            AND activities.creator_id = auth.uid()
        )
    );

-- 用户可以更新邀请码（使用邀请码时）
CREATE POLICY "Users can use invite codes" ON invite_codes
    FOR UPDATE USING (
        -- 邀请码未被使用
        is_used = false AND
        -- 活动仍然激活
        EXISTS (
            SELECT 1 FROM activities 
            WHERE activities.id = invite_codes.activity_id 
            AND activities.is_active = true
            AND (activities.end_date IS NULL OR activities.end_date > NOW())
        )
    ) WITH CHECK (
        -- 只能设置为已使用，并且使用者是当前用户
        is_used = true AND used_by = auth.uid()
    );

-- 用户订阅表策略
-- 用户只能查看自己的订阅
CREATE POLICY "Users can view own subscriptions" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

-- 用户可以创建自己的订阅
CREATE POLICY "Users can create own subscriptions" ON user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 用户可以删除自己的订阅
CREATE POLICY "Users can delete own subscriptions" ON user_subscriptions
    FOR DELETE USING (auth.uid() = user_id);

-- 反馈表策略
-- 用户只能查看自己的反馈
CREATE POLICY "Users can view own feedback" ON feedbacks
    FOR SELECT USING (auth.uid() = user_id);

-- 活动创建者可以查看活动的所有反馈
CREATE POLICY "Activity creators can view activity feedback" ON feedbacks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM activities 
            WHERE activities.id = feedbacks.activity_id 
            AND activities.creator_id = auth.uid()
        )
    );

-- 用户可以创建反馈
CREATE POLICY "Users can create feedback" ON feedbacks
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        -- 确保用户使用了对应的邀请码
        EXISTS (
            SELECT 1 FROM invite_codes 
            WHERE invite_codes.id = feedbacks.code_id 
            AND invite_codes.used_by = auth.uid()
            AND invite_codes.activity_id = feedbacks.activity_id
        )
    );

-- 创建安全函数来获取当前用户信息
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS UUID AS $$
BEGIN
    RETURN auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建函数来检查用户是否为VIP
CREATE OR REPLACE FUNCTION is_user_vip(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_id AND is_vip = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建函数来检查用户是否已实名认证
CREATE OR REPLACE FUNCTION is_user_verified(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_id AND is_verified = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
