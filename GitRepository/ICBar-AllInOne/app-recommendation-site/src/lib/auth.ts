import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import { SupabaseAdapter } from '@next-auth/supabase-adapter';
import { supabase } from './supabase';
import { userOperations } from './database';

export const authOptions: NextAuthOptions = {
  adapter: SupabaseAdapter({
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  }),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async session({ session, user }) {
      if (session.user && user) {
        // 确保session包含用户ID
        session.user.id = user.id;
        
        // 从数据库获取最新的用户信息
        try {
          const userData = await userOperations.getUser(user.id);
          if (userData) {
            session.user.is_verified = userData.is_verified;
            session.user.is_vip = userData.is_vip;
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      }
      return session;
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google' && profile) {
        try {
          // 同步用户信息到我们的用户表
          await userOperations.upsertUser({
            id: user.id,
            email: user.email!,
            name: user.name || undefined,
            avatar_url: user.image || undefined,
          });
        } catch (error) {
          console.error('Error syncing user data:', error);
          return false;
        }
      }
      return true;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: 'database',
  },
  debug: process.env.NODE_ENV === 'development',
};

// 扩展NextAuth类型
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string;
      image?: string;
      is_verified?: boolean;
      is_vip?: boolean;
    };
  }

  interface User {
    id: string;
    email: string;
    name?: string;
    image?: string;
    is_verified?: boolean;
    is_vip?: boolean;
  }
}
