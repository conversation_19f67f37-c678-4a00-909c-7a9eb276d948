import { supabase, supabaseAdmin } from './supabase';
import type { 
  User, 
  App, 
  Activity, 
  InviteCode, 
  UserSubscription, 
  Feedback,
  PaginatedResponse,
  CreateAppForm,
  CreateActivityForm
} from '@/types';

// 用户相关操作
export const userOperations = {
  // 获取用户信息
  async getUser(userId: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) throw error;
    return data;
  },

  // 更新用户信息
  async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // 创建或更新用户（用于认证后的用户同步）
  async upsertUser(user: Partial<User>): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .upsert(user, { onConflict: 'id' })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
};

// 应用相关操作
export const appOperations = {
  // 获取应用列表（支持分页和筛选）
  async getApps(params: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    sort?: 'activity_count' | 'created_at' | 'name';
    order?: 'asc' | 'desc';
  } = {}): Promise<PaginatedResponse<App>> {
    const {
      page = 1,
      limit = 12,
      category,
      search,
      sort = 'activity_count',
      order = 'desc'
    } = params;

    let query = supabase
      .from('apps')
      .select(`
        *,
        creator:users(id, name, avatar_url)
      `, { count: 'exact' })
      .eq('is_active', true);

    // 分类筛选
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    // 搜索
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // 排序
    query = query.order(sort, { ascending: order === 'asc' });

    // 分页
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;
    
    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  },

  // 获取单个应用详情
  async getApp(appId: string): Promise<App | null> {
    const { data, error } = await supabase
      .from('apps')
      .select(`
        *,
        creator:users(id, name, avatar_url)
      `)
      .eq('id', appId)
      .single();
    
    if (error) throw error;
    return data;
  },

  // 创建应用
  async createApp(appData: CreateAppForm, creatorId: string): Promise<App> {
    const { data, error } = await supabase
      .from('apps')
      .insert({
        ...appData,
        creator_id: creatorId
      })
      .select(`
        *,
        creator:users(id, name, avatar_url)
      `)
      .single();
    
    if (error) throw error;
    return data;
  },

  // 更新应用
  async updateApp(appId: string, updates: Partial<CreateAppForm>): Promise<App> {
    const { data, error } = await supabase
      .from('apps')
      .update(updates)
      .eq('id', appId)
      .select(`
        *,
        creator:users(id, name, avatar_url)
      `)
      .single();
    
    if (error) throw error;
    return data;
  },

  // 删除应用
  async deleteApp(appId: string): Promise<void> {
    const { error } = await supabase
      .from('apps')
      .delete()
      .eq('id', appId);
    
    if (error) throw error;
  }
};

// 活动相关操作
export const activityOperations = {
  // 获取应用的活动列表
  async getAppActivities(appId: string): Promise<Activity[]> {
    const { data, error } = await supabase
      .from('activities')
      .select(`
        *,
        app:apps(id, name, icon_url),
        creator:users(id, name, avatar_url)
      `)
      .eq('app_id', appId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  // 获取活动详情
  async getActivity(activityId: string): Promise<Activity | null> {
    const { data, error } = await supabase
      .from('activities')
      .select(`
        *,
        app:apps(id, name, icon_url, creator_id),
        creator:users(id, name, avatar_url)
      `)
      .eq('id', activityId)
      .single();
    
    if (error) throw error;
    return data;
  },

  // 创建活动
  async createActivity(activityData: CreateActivityForm, creatorId: string): Promise<Activity> {
    const { codes, ...activityInfo } = activityData;
    
    // 开始事务
    const { data: activity, error: activityError } = await supabase
      .from('activities')
      .insert({
        ...activityInfo,
        creator_id: creatorId,
        total_codes: codes.length,
        remaining_codes: codes.length
      })
      .select()
      .single();
    
    if (activityError) throw activityError;

    // 插入邀请码
    const inviteCodes = codes.map(code => ({
      activity_id: activity.id,
      code: code.trim()
    }));

    const { error: codesError } = await supabase
      .from('invite_codes')
      .insert(inviteCodes);
    
    if (codesError) throw codesError;

    return activity;
  },

  // 更新活动
  async updateActivity(activityId: string, updates: Partial<Activity>): Promise<Activity> {
    const { data, error } = await supabase
      .from('activities')
      .update(updates)
      .eq('id', activityId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
};

// 邀请码相关操作
export const inviteCodeOperations = {
  // 使用邀请码
  async useInviteCode(code: string, activityId: string, userId: string): Promise<InviteCode> {
    const { data, error } = await supabase
      .from('invite_codes')
      .update({
        is_used: true,
        used_by: userId,
        used_at: new Date().toISOString()
      })
      .eq('code', code)
      .eq('activity_id', activityId)
      .eq('is_used', false)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // 获取活动的邀请码统计
  async getActivityCodeStats(activityId: string): Promise<{
    total: number;
    used: number;
    remaining: number;
  }> {
    const { data, error } = await supabase
      .from('invite_codes')
      .select('is_used')
      .eq('activity_id', activityId);
    
    if (error) throw error;

    const total = data.length;
    const used = data.filter(code => code.is_used).length;
    const remaining = total - used;

    return { total, used, remaining };
  }
};

// 用户订阅相关操作
export const subscriptionOperations = {
  // 订阅应用
  async subscribeToApp(userId: string, appId: string): Promise<UserSubscription> {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .insert({
        user_id: userId,
        app_id: appId
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // 取消订阅
  async unsubscribeFromApp(userId: string, appId: string): Promise<void> {
    const { error } = await supabase
      .from('user_subscriptions')
      .delete()
      .eq('user_id', userId)
      .eq('app_id', appId);
    
    if (error) throw error;
  },

  // 检查是否已订阅
  async isSubscribed(userId: string, appId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .select('id')
      .eq('user_id', userId)
      .eq('app_id', appId)
      .single();
    
    return !error && !!data;
  },

  // 获取用户订阅的应用
  async getUserSubscriptions(userId: string): Promise<App[]> {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .select(`
        app:apps(
          *,
          creator:users(id, name, avatar_url)
        )
      `)
      .eq('user_id', userId);
    
    if (error) throw error;
    return data?.map(sub => sub.app).filter(Boolean) || [];
  }
};

// 反馈相关操作
export const feedbackOperations = {
  // 提交反馈
  async submitFeedback(feedbackData: {
    activity_id: string;
    user_id: string;
    code_id: string;
    answers: Record<string, string>;
  }): Promise<Feedback> {
    const { data, error } = await supabase
      .from('feedbacks')
      .insert(feedbackData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // 获取活动的反馈
  async getActivityFeedbacks(activityId: string): Promise<Feedback[]> {
    const { data, error } = await supabase
      .from('feedbacks')
      .select('*')
      .eq('activity_id', activityId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }
};
