import { z } from 'zod';

// 应用创建验证
export const createAppSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符'),
  description: z.string().min(10, '应用描述至少需要10个字符').max(1000, '应用描述不能超过1000个字符'),
  category: z.enum([
    'productivity',
    'entertainment', 
    'education',
    'business',
    'lifestyle',
    'utilities',
    'games',
    'social',
    'other'
  ], { errorMap: () => ({ message: '请选择有效的应用分类' }) }),
  website_url: z.string().url('请输入有效的网站URL').optional().or(z.literal('')),
  download_url: z.string().url('请输入有效的下载URL').optional().or(z.literal('')),
  icon_url: z.string().url('请输入有效的图标URL').optional().or(z.literal(''))
});

// 送码活动创建验证
export const createActivitySchema = z.object({
  app_id: z.string().uuid('请选择有效的应用'),
  title: z.string().min(1, '活动标题不能为空').max(200, '活动标题不能超过200个字符'),
  description: z.string().min(10, '活动描述至少需要10个字符').max(2000, '活动描述不能超过2000个字符'),
  codes: z.array(z.string().min(1, '邀请码不能为空')).min(1, '至少需要提供一个邀请码'),
  requires_verification: z.boolean().optional(),
  requires_subscription: z.boolean().optional(),
  feedback_required: z.boolean().optional(),
  feedback_questions: z.array(z.string()).optional(),
  end_date: z.string().datetime().optional()
});

// 用户注册验证
export const userRegistrationSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符').optional(),
});

// 反馈提交验证
export const feedbackSchema = z.object({
  activity_id: z.string().uuid('无效的活动ID'),
  code_id: z.string().uuid('无效的邀请码ID'),
  answers: z.record(z.string(), z.string().min(1, '回答不能为空'))
});

// 搜索和筛选验证
export const searchSchema = z.object({
  query: z.string().max(100, '搜索关键词不能超过100个字符').optional(),
  category: z.enum([
    'all',
    'productivity',
    'entertainment',
    'education', 
    'business',
    'lifestyle',
    'utilities',
    'games',
    'social',
    'other'
  ]).optional(),
  sort: z.enum(['activity_count', 'created_at', 'name']).optional(),
  order: z.enum(['asc', 'desc']).optional(),
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).max(50).optional()
});

// 邀请码使用验证
export const useCodeSchema = z.object({
  code: z.string().min(1, '邀请码不能为空'),
  activity_id: z.string().uuid('无效的活动ID')
});

export type CreateAppInput = z.infer<typeof createAppSchema>;
export type CreateActivityInput = z.infer<typeof createActivitySchema>;
export type UserRegistrationInput = z.infer<typeof userRegistrationSchema>;
export type FeedbackInput = z.infer<typeof feedbackSchema>;
export type SearchInput = z.infer<typeof searchSchema>;
export type UseCodeInput = z.infer<typeof useCodeSchema>;
