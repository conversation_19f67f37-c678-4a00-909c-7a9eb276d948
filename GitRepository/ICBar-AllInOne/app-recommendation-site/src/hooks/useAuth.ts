import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

export function useAuth() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const user = session?.user;
  const isLoading = status === 'loading';
  const isAuthenticated = !!user;

  const login = useCallback(async (provider: string = 'google') => {
    try {
      await signIn(provider, { callbackUrl: '/' });
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      await signOut({ callbackUrl: '/' });
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }, []);

  const requireAuth = useCallback((redirectTo: string = '/auth/signin') => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo);
      return false;
    }
    return true;
  }, [isLoading, isAuthenticated, router]);

  const requireVip = useCallback((redirectTo: string = '/upgrade') => {
    if (!isLoading && (!isAuthenticated || !user?.is_vip)) {
      router.push(redirectTo);
      return false;
    }
    return true;
  }, [isLoading, isAuthenticated, user?.is_vip, router]);

  const requireVerification = useCallback((redirectTo: string = '/verify') => {
    if (!isLoading && (!isAuthenticated || !user?.is_verified)) {
      router.push(redirectTo);
      return false;
    }
    return true;
  }, [isLoading, isAuthenticated, user?.is_verified, router]);

  return {
    user,
    isLoading,
    isAuthenticated,
    isVip: user?.is_vip || false,
    isVerified: user?.is_verified || false,
    login,
    logout,
    requireAuth,
    requireVip,
    requireVerification,
  };
}
