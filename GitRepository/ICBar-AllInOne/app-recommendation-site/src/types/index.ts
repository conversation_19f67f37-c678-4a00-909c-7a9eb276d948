// 用户相关类型
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  is_verified: boolean;
  is_vip: boolean;
  created_at: string;
  updated_at: string;
}

// 应用相关类型
export interface App {
  id: string;
  name: string;
  description: string;
  icon_url?: string;
  category: string;
  website_url?: string;
  download_url?: string;
  creator_id: string;
  activity_count: number;
  total_codes_distributed: number;
  created_at: string;
  updated_at: string;
  creator?: User;
}

// 送码活动类型
export interface Activity {
  id: string;
  app_id: string;
  creator_id: string;
  title: string;
  description: string;
  total_codes: number;
  remaining_codes: number;
  is_active: boolean;
  requires_verification?: boolean;
  requires_subscription?: boolean;
  feedback_required?: boolean;
  feedback_questions?: string[];
  start_date: string;
  end_date?: string;
  created_at: string;
  updated_at: string;
  app?: App;
  creator?: User;
}

// 邀请码类型
export interface InviteCode {
  id: string;
  activity_id: string;
  code: string;
  is_used: boolean;
  used_by?: string;
  used_at?: string;
  created_at: string;
}

// 用户订阅类型
export interface UserSubscription {
  id: string;
  user_id: string;
  app_id: string;
  created_at: string;
}

// 反馈类型
export interface Feedback {
  id: string;
  activity_id: string;
  user_id: string;
  code_id: string;
  answers: Record<string, string>;
  created_at: string;
}

// API响应类型
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// 分页类型
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 表单类型
export interface CreateAppForm {
  name: string;
  description: string;
  category: string;
  website_url?: string;
  download_url?: string;
  icon_url?: string;
}

export interface CreateActivityForm {
  app_id: string;
  title: string;
  description: string;
  codes: string[];
  requires_verification?: boolean;
  requires_subscription?: boolean;
  feedback_required?: boolean;
  feedback_questions?: string[];
  end_date?: string;
}

// 应用分类
export type AppCategory = 
  | 'productivity'
  | 'entertainment'
  | 'education'
  | 'business'
  | 'lifestyle'
  | 'utilities'
  | 'games'
  | 'social'
  | 'other';

// 活动状态
export type ActivityStatus = 'active' | 'ended' | 'draft';

// 排序选项
export type SortOption = 'activity_count' | 'created_at' | 'name';
export type SortOrder = 'asc' | 'desc';
